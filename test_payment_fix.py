#!/usr/bin/env python3
"""
Test script to verify the appointment payment fix works correctly.
This tests that patients no longer need payment profiles for appointment payments.
"""

import os
import sys
import django
from unittest.mock import MagicMock, patch

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ravid.settings')
sys.path.append('/home/<USER>/partime-work/ravid-server')

try:
    django.setup()
except Exception as e:
    print(f"Django setup failed: {e}")
    sys.exit(1)

def test_patient_without_payment_profile():
    """Test that patients can make payments without payment profiles"""
    print("🧪 Testing: Patient without payment profile can make appointment payment")
    print("=" * 70)
    
    try:
        from django.contrib.auth import get_user_model
        from billing.services.appointment_payment_service import AppointmentPaymentService
        from billing.models import UserPaymentProfile, Customer
        from appointments.models import Appointment
        from doctors.models import DoctorConsultationProfile
        from django.utils import timezone
        from datetime import timedelta
        
        User = get_user_model()
        
        # Create test users
        patient = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Patient'
        )
        
        doctor = User.objects.create_user(
            email='<EMAIL>', 
            password='testpass123',
            first_name='Dr. Test',
            last_name='Doctor'
        )
        
        # Create doctor payment profile (required)
        doctor_profile = UserPaymentProfile.objects.create(
            user=doctor,
            stripe_account_id='acct_test_doctor123',
            charges_enabled=True,
            payouts_enabled=True,
            details_submitted=True,
            is_verified=True
        )
        
        # Create doctor consultation profile
        consultation_profile = DoctorConsultationProfile.objects.create(
            user=doctor,
            consultation_fee=7500,  # $75
            consultation_duration=45,
            accepts_telemedicine=True,
            bio="Test doctor bio"
        )
        
        # Create appointment
        appointment = Appointment.objects.create(
            creator=patient,
            patient=patient,
            doctor=doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Test consultation"
        )
        
        print(f"✅ Created patient: {patient.email} (NO payment profile)")
        print(f"✅ Created doctor: {doctor.email} (WITH payment profile)")
        print(f"✅ Created appointment: {appointment.id}")
        
        # Verify patient has NO payment profile
        patient_has_profile = hasattr(patient, 'payment_profile')
        print(f"📋 Patient has payment profile: {patient_has_profile}")
        
        # Verify doctor HAS payment profile  
        doctor_has_profile = hasattr(doctor, 'payment_profile')
        print(f"📋 Doctor has payment profile: {doctor_has_profile}")
        
        # Test the payment service
        service = AppointmentPaymentService()
        
        # Mock Stripe calls
        with patch('stripe.checkout.Session.create') as mock_checkout, \
             patch('stripe.Account.retrieve') as mock_account, \
             patch('billing.services.customer_service.stripe.Customer.create') as mock_customer_create:
            
            # Mock Stripe customer creation
            mock_customer_create.return_value = MagicMock(id='cus_test_patient123')
            
            # Mock Stripe account retrieval for doctor
            mock_account.return_value = MagicMock(
                charges_enabled=True,
                payouts_enabled=True,
                details_submitted=True,
                capabilities={'transfers': 'active'}
            )
            
            # Mock checkout session creation
            mock_checkout.return_value = MagicMock(
                id='cs_test_123',
                url='https://checkout.stripe.com/pay/cs_test_123'
            )
            
            # This should work now - patient doesn't need payment profile
            try:
                transfer, checkout_url = service.create_appointment_checkout_session(
                    patient=patient,
                    doctor=doctor,
                    appointment=appointment,
                    consultation_fee=consultation_profile.consultation_fee
                )
                
                print("✅ SUCCESS: Payment checkout session created without patient payment profile!")
                print(f"   Transfer ID: {transfer.id}")
                print(f"   Checkout URL: {checkout_url}")
                print(f"   Amount: ${consultation_profile.consultation_fee / 100}")
                
                # Verify customer was created for patient
                patient_customer = Customer.objects.filter(user=patient).first()
                if patient_customer:
                    print(f"✅ Stripe customer created for patient: {patient_customer.stripe_customer_id}")
                else:
                    print("⚠️  No customer record found (but this might be expected in test)")
                
                # Verify appointment was updated
                appointment.refresh_from_db()
                print(f"✅ Appointment payment status: {appointment.payment_status}")
                print(f"✅ Appointment linked to transfer: {appointment.payment_transfer is not None}")
                
                return True
                
            except Exception as e:
                print(f"❌ FAILED: {str(e)}")
                return False
                
    except Exception as e:
        print(f"❌ Test setup failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_doctor_still_needs_payment_profile():
    """Test that doctors still need payment profiles"""
    print("\n🧪 Testing: Doctor without payment profile should fail")
    print("=" * 70)
    
    try:
        from django.contrib.auth import get_user_model
        from billing.services.appointment_payment_service import AppointmentPaymentService
        from billing.exceptions import PaymentError
        from appointments.models import Appointment
        from django.utils import timezone
        from datetime import timedelta
        
        User = get_user_model()
        
        # Create test users
        patient = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Patient2'
        )
        
        doctor = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123', 
            first_name='Dr. Test',
            last_name='Doctor2'
        )
        
        # Create appointment
        appointment = Appointment.objects.create(
            creator=patient,
            patient=patient,
            doctor=doctor,
            appointment_type='booking',
            mode='video_call',
            direct_payment=True,
            start_time=timezone.now() + timedelta(hours=1),
            end_time=timezone.now() + timedelta(hours=2),
            title="Test consultation 2"
        )
        
        print(f"✅ Created patient: {patient.email}")
        print(f"✅ Created doctor: {doctor.email} (NO payment profile)")
        
        # Test the payment service - should fail
        service = AppointmentPaymentService()
        
        try:
            transfer, checkout_url = service.create_appointment_checkout_session(
                patient=patient,
                doctor=doctor,
                appointment=appointment,
                consultation_fee=7500
            )
            
            print("❌ UNEXPECTED: Should have failed but didn't!")
            return False
            
        except PaymentError as e:
            if "Doctor must have a payment profile" in str(e):
                print("✅ SUCCESS: Correctly rejected doctor without payment profile")
                print(f"   Error message: {str(e)}")
                return True
            else:
                print(f"❌ Wrong error message: {str(e)}")
                return False
                
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Appointment Payment Fix")
    print("=" * 70)
    print("This test verifies that:")
    print("1. Patients can make payments WITHOUT payment profiles")
    print("2. Doctors still NEED payment profiles to receive payments")
    print("3. The fix resolves the '500 Internal Server Error' issue")
    print()
    
    test1_result = test_patient_without_payment_profile()
    test2_result = test_doctor_still_needs_payment_profile()
    
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS:")
    print(f"   Patient without payment profile: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"   Doctor without payment profile:  {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 ALL TESTS PASSED!")
        print("The appointment payment fix is working correctly.")
        print("\n📋 Summary of changes:")
        print("   • Removed requirement for patient payment profiles")
        print("   • Patients now use Stripe Customer (created automatically)")
        print("   • Doctors still require UserPaymentProfile with Connect account")
        print("   • Payment flow: Patient → Stripe Checkout → Platform → Doctor")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("Please check the implementation.")
