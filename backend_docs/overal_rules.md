# Django App Creation Guidelines

# Cách source hoạt động
Source này chạy bằng docker, hã<PERSON> tham khảo Make dev file để biết cách hoạt động


# Định nghĩa lại các khái niệm trong SaaS của bạn
Service:
Dịch vụ trả phí một lần (one-time payment), ví dụ: mua một lần một báo cáo, một lần khám, v.v.
Subscription:
Dịch vụ trả phí định kỳ (monthly/yearly), dành cho user cá nhân hoặc doanh nghiệp nhỏ, không cần quản lý nhóm user.
Solution (Enterprise Solution):
Gói dịch vụ dành cho enterprise (doanh nghiệp, bệnh viện, clinic, tổ chức lớn), cho phép nhiều user cùng thuộc một enterprise và quản lý tập trung (user group, phân quyền, quản lý seat, v.v).
Khi user nâng cấp lên enterprise, họ sẽ có quyền tạo enterprise, mời user kh<PERSON>c vào, phân quyền, quản lý tài nguyên chung.

## Always write comments in english, clean, industry standard
## General Rules for Creating a New Django App
- Use **Serializers**, **Views**, and **Models** and **Services** in a clean, industry-standard manner to ensure maintainability and scalability. Tham khảo /home/<USER>/partime-work/ravid-server/appointments app for how to structure folder (but this app is still not clean). But the main idea is store views and serializer in /home/<USER>/partime-work/ravid-server/appointments/api, models in /home/<USER>/partime-work/ravid-server/appointments/models, services in /home/<USER>/partime-work/ravid-server/appointments/services
- For View use viewsets.ModelViewSet
- Do not write all in one file, split files for more clean, reuse and easy to maintain.
- Each file shouldn't long over 300 lines


## Locations of Important Configuration Files in the Project
- The project runs using **Docker containers**, so Django is not built manually. Instead, it is run via Docker using a **Makefile** with the command `make dev`, located at `/home/<USER>/partime-work/ravid-server/Makefile`.
- The requirements for the libraries used are stored in `/home/<USER>/partime-work/ravid-server/requirements`. When adding a new library, update the file `/home/<USER>/partime-work/ravid-server/requirements/base.txt`.
- The folder containing configurations and initialization for **URLs**, **variables**, **base models**, etc., is located at `/home/<USER>/partime-work/ravid-server/config`. The most important configuration files are located in `/home/<USER>/partime-work/ravid-server/config/settings/base.py`.

## Cấu trúc folder app chung chuẩn
app_name/
├── __init__.py
├── api/
│   ├── __init__.py
│   ├── model1/
│   │   ├── __init__.py
│   │   ├── serializers.py
│   │   ├── views.py
│   │   └── urls.py
│   ├── model2/
│   │   ├── __init__.py
│   │   ├── serializers.py
│   │   ├── views.py
│   │   └── urls.py
│   └── common/
│       ├── __init__.py
│       ├── mixins.py
│       ├── permissions.py
│       └── pagination.py
├── models/
│   ├── __init__.py
│   ├── model1.py
│   ├── model2.py
│   └── common.py
├── services/
│   ├── __init__.py
│   ├── model1_service.py
│   ├── model2_service.py
│   └── common_service.py
├── tests/
│   ├── __init__.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── test_model1.py
│   │   └── test_model2.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── test_model1_service.py
│   │   └── test_model2_service.py
│   └── api/
│       ├── __init__.py
│       ├── test_model1_api.py
│       └── test_model2_api.py
├── constants.py
├── exceptions.py
└── apps.py

Giải thích cấu trúc
1. Thư mục api/
Mỗi model có thư mục riêng chứa serializers, views và urls
Thư mục  common/ chứa các thành phần dùng chung như mixins, permissions, pagination
2. Thư mục models/
Mỗi model được định nghĩa trong file riêng
File common.py chứa các model dùng chung hoặc abstract models
3. Thư mục services/
Mỗi model có service riêng
File common_service.py chứa các service dùng chung
4. Thư mục tests/
Được tổ chức theo cấu trúc tương tự như code
Mỗi model có test riêng cho models, services và API
5. File exceptions.py
Định nghĩa các custom exceptions cho app