#!/usr/bin/env python3
"""
Quick test script to verify the Stripe Connect account creation fix.
This script tests the fixed API endpoint without making actual Stripe calls.
"""

import os
import sys
import django
from unittest.mock import patch, <PERSON><PERSON>

# Add the project root to Python path
sys.path.insert(0, '/home/<USER>/partime-work/ravid-server')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from django.test import TestCase
from django.contrib.auth import get_user_model
from billing.services.user_transfer_service import UserTransferService
from billing.models import UserPaymentProfile

User = get_user_model()

def test_stripe_connect_fix():
    """Test that the Stripe Connect account creation no longer has parameter conflicts"""
    
    print("🧪 Testing Stripe Connect Account Creation Fix")
    print("=" * 60)
    
    # Create a test user
    user = User.objects.create_user(
        email='<EMAIL>',
        password='testpass123',
        first_name='Test',
        last_name='Doctor'
    )
    
    # Mock Stripe API calls to avoid actual API requests
    with patch('stripe.Account.create') as mock_account_create, \
         patch('stripe.AccountLink.create') as mock_account_link_create:
        
        # Setup mock responses
        mock_account_create.return_value = Mock(id='acct_test_123')
        mock_account_link_create.return_value = Mock(
            url='https://connect.stripe.com/express/onboarding/test_link'
        )
        
        try:
            # Test the fixed service
            service = UserTransferService()
            account_link = service.create_stripe_connect_account(user)
            
            print("✅ SUCCESS: Stripe Connect account creation completed without errors")
            print(f"   Account link: {account_link}")
            
            # Verify the correct parameters were used
            mock_account_link_create.assert_called_once()
            call_args = mock_account_link_create.call_args
            
            # Check that 'collect' parameter is NOT used
            if 'collect' in call_args.kwargs:
                print("❌ ERROR: Deprecated 'collect' parameter still being used")
                return False
            
            # Check that 'collection_options' parameter IS used
            if 'collection_options' not in call_args.kwargs:
                print("❌ ERROR: 'collection_options' parameter not found")
                return False
            
            print("✅ SUCCESS: Correct parameters used (collection_options instead of collect)")
            print(f"   Parameters used: {list(call_args.kwargs.keys())}")
            
            # Verify UserPaymentProfile was created
            profile = UserPaymentProfile.objects.get(user=user)
            print(f"✅ SUCCESS: UserPaymentProfile created with Stripe account ID: {profile.stripe_account_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ ERROR: {str(e)}")
            return False
        
        finally:
            # Cleanup
            user.delete()

def test_refresh_account_link_fix():
    """Test that the refresh account link also uses correct parameters"""
    
    print("\n🔄 Testing Refresh Account Link Fix")
    print("=" * 60)
    
    # Create a test user with existing payment profile
    user = User.objects.create_user(
        email='<EMAIL>',
        password='testpass123',
        first_name='Test',
        last_name='Doctor2'
    )
    
    # Create payment profile
    profile = UserPaymentProfile.objects.create(
        user=user,
        stripe_account_id='acct_existing_123'
    )
    
    with patch('stripe.AccountLink.create') as mock_account_link_create:
        
        mock_account_link_create.return_value = Mock(
            url='https://connect.stripe.com/express/onboarding/refresh_link'
        )
        
        try:
            from django.test import RequestFactory
            from django.contrib.auth.models import AnonymousUser
            from billing.views.user_transfer_views import UserPaymentProfileViewSet
            
            # Create a mock request
            factory = RequestFactory()
            request = factory.post('/api/billing/payment-profiles/refresh_connect_account_link/')
            request.user = user
            
            # Test the view
            viewset = UserPaymentProfileViewSet()
            viewset.request = request
            
            response = viewset.refresh_connect_account_link(request)
            
            print("✅ SUCCESS: Refresh account link completed without errors")
            
            # Verify the correct parameters were used
            mock_account_link_create.assert_called_once()
            call_args = mock_account_link_create.call_args
            
            # Check that 'collect' parameter is NOT used
            if 'collect' in call_args.kwargs:
                print("❌ ERROR: Deprecated 'collect' parameter still being used in refresh")
                return False
            
            # Check that 'collection_options' parameter IS used
            if 'collection_options' not in call_args.kwargs:
                print("❌ ERROR: 'collection_options' parameter not found in refresh")
                return False
            
            print("✅ SUCCESS: Refresh link uses correct parameters")
            
            return True
            
        except Exception as e:
            print(f"❌ ERROR in refresh test: {str(e)}")
            return False
        
        finally:
            # Cleanup
            user.delete()

if __name__ == '__main__':
    print("🚀 Starting Stripe Connect Fix Verification")
    print("=" * 60)
    
    success1 = test_stripe_connect_fix()
    success2 = test_refresh_account_link_fix()
    
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 ALL TESTS PASSED! The Stripe Connect fix is working correctly.")
        print("\n✅ Fixed Issues:")
        print("   - Removed deprecated 'collect' parameter")
        print("   - Added proper 'collection_options' parameter")
        print("   - Both account creation and refresh work correctly")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
        sys.exit(1)
